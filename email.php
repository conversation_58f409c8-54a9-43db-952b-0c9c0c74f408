<?php
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

use <PERSON><PERSON>Mailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\Exception;

require_once "vendor/autoload.php";

if (isset($_POST['g-recaptcha-response'])) {
	$data = array(
		'secret' => '6LcsyCkUAAAAAPlLXTtsFI_jlFaiLZcG8Q_J_3qB',
		'response' => $_POST['g-recaptcha-response']
	);
	$options = array(
		'http' => array(
			'header'  => "Content-type: application/x-www-form-urlencoded\r\n",
			'method'  => 'POST',
			'content' => http_build_query($data)
		)
	);
	$context  = stream_context_create($options);
	$response = file_get_contents("https://www.google.com/recaptcha/api/siteverify", false, $context);
	$response = json_decode($response);
	$gcaptcharesult = $response->success;
}

$informacoes = array(
	'host' => 'email-ssl.com.br',
	'port' => '587',
	'username' => '<EMAIL>',
	'sender' => '<EMAIL>',
	'password' => 'Pia*212223',
	'destinatario_nome' => 'ABCR',
	'remetente_nome' => 'ABCR',
	'email1' => '<EMAIL>',
	'email2' => '<EMAIL>'
);

$mail = new PHPMailer;

$mail->setLanguage("pt_br");
$mail->CharSet = 'UTF-8';

//Disable SMTP debugging. 
$mail->SMTPDebug = false;
//Set PHPMailer to use SMTP.
$mail->isSMTP();
//Set SMTP host name
$mail->Host = $informacoes['host'];
//Set this to true if SMTP host requires authentication to send email
$mail->SMTPAuth = true;
//Provide username and password
$mail->Username = $informacoes['username'];
$mail->Password = $informacoes['password'];
$mail->SMTPSecure = false;

//Set TCP port to connect to 
$mail->Port = $informacoes['port'];
$mail->SMTPDebug = 0;
$mail->From = $informacoes['sender'];
$mail->FromName = $informacoes['remetente_nome'];

$mail->addAddress($informacoes['email1'], $informacoes['destinatario_nome']);
(isset($informacoes['email2']) && $informacoes['email2']) ? $mail->addBCC($informacoes['email2']) : '';
(isset($informacoes['email3']) && $informacoes['email3']) ? $mail->addCC($informacoes['email3']) : '';

$mail->addBCC("<EMAIL>", "Robison");
$mail->addBCC("<EMAIL>", "Starter");

$mail->isHTML(true);

if (isset($_POST['google_ads']) && $_POST['google_ads'] == '1') {
	$mail->Subject = '[Google Ads] - Contato do EMPREENDIMENTO - Piazza 685';
} else {
	$mail->Subject = 'Contato através do site - Piazza 685';
}

$conteudo = ""; //"<p>E-mail enviado de {$informacoes['url']}</p>";

if (isset($_POST['nome'])) $conteudo .= "<p>Nome: {$_POST['nome']}</p>";
if (isset($_POST['telefone'])) $conteudo .= "<p>Telefone: {$_POST['telefone']}</p>";
if (isset($_POST['email'])) $conteudo .= "<p>E-mail: {$_POST['email']}</p>";
if (isset($_POST['contato'])) $conteudo .= "<p>Prefiro contato via: {$_POST['contato']}</p>";
if (isset($_POST['mensagem'])) $conteudo .= "<p>Mensagem: {$_POST['mensagem']}</p>";

$mail->Body = $conteudo;

if ($_SERVER['SERVER_NAME'] != 'localhost' && $gcaptcharesult) {
	if (!$mail->send()) {
		echo "Mailer Error: " . $mail->ErrorInfo;
	} else {
		//echo 'enviado';
		// Mensagem enviada com sucesso
	}
} else {
	//print 'Erro no recaptcha';
	header('Location: https://piazza685.com.br/');
	die();
}
?>
<!doctype html>
<html>

<head>
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta http-equiv="imagetoolbar" content="no" />
	<title>Piazza 685 - ABCR Empreendimentos</title>
	<meta name="description" content="ABCR Empreendimentos" />
	<meta name="keywords" content="ABCR Empreendimentos imóveis" />
	<meta name="distribution" content="global" />
	<meta name="producer" content="Promentor" />
	<meta name="robots" content="ALL" />
	<meta name="rating" content="General" />
	<meta name="language" content="pt-br" />
	<meta name="copyright" content="Promentor - Copyright 2022" />
	<meta name="url" content="https://www.piazza685.com.br/" />
	<meta name="autor" content="Equipe Promentor" />
	<meta name="company" content="Promentor" />
	<meta name="producer" content="Promentor" />
	<meta name="metatagupdate" content="Mon, 29 Set 2022 08:30:50 -0300" />
	<meta name="revisit-after" content="1" />
	<meta name="title" content="ABCR Empreendimentos" />
	<meta http-equiv="cache-control" content="public" />
	<meta http-equiv="Pragma" content="public">

	<?php
	$host = '/';
	if ($_SERVER['HTTP_HOST'] == 'localhost') $host = 'http://localhost/piazza685/';
	if (isset($_GET['page']) && $_GET['page'] == 'piazza685-old') $host .= 'piazza685/';
	?>
	<base href="<?= $host ?>">

	<meta property="og:url" content="https://www.piazza685.com.br/" />
	<meta property="og:type" content="article" />
	<meta property="og:title" content="ABCR Empreendimentos" />
	<meta property="og:description" content="" />
	<meta property="og:image" content="" />
	<link rel="canonical" href="https://www.piazza685.com.br/" />

	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&display=swap" rel="stylesheet">
	<link rel="stylesheet" href="assets/css/geral.css">
	<link rel="stylesheet" href="assets/css/inicial.css">
	<link rel="stylesheet" href="assets/css/fullpage.css">
</head>

<body id="app">

	<?php include('includes/topo.php'); ?>

	<div id="fullpage">

		<div class="section">
			<div class="imovel lalique">
				<div class="conteudo">
					<h2 class="nome">Mensagem enviada!</h2>
					<p class="data">Em breve retornaremos o seu contato!</p>
				</div>
			</div>
		</div>

		<div class="section fp-auto-height" data-anchor="slide-contato">
			<?php
			$_GET['page'] = 'faleconosco';
			include('includes/rodape.php');
			?>
		</div>

	</div>

	<?php include('includes/chat.php'); ?>

	<script src="https://www.google.com/recaptcha/api.js" async defer></script>
	<script type="text/javascript" src="assets/js/jquery-3.6.0.min.js"></script>
	<script type="text/javascript" src="assets/js/fullpage.js"></script>
	<script type="text/javascript" src="assets/js/geral.js"></script>
	<script type="text/javascript" src="assets/js/inicial.js"></script>

</body>

</html>