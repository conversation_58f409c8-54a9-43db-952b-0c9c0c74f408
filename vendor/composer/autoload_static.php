<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitfc123c8d64b2e6a77c41d2824a60af2c
{
    public static $prefixLengthsPsr4 = array (
        'P' => 
        array (
            'PHPMailer\\PHPMailer\\' => 20,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'PHPMailer\\PHPMailer\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpmailer/phpmailer/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInitfc123c8d64b2e6a77c41d2824a60af2c::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInitfc123c8d64b2e6a77c41d2824a60af2c::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInitfc123c8d64b2e6a77c41d2824a60af2c::$classMap;

        }, null, ClassLoader::class);
    }
}
