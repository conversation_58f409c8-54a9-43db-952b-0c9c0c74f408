const fp = new fullpage('#fullpage', {
	//options here
	autoScrolling:true,
	scrollHorizontally: true,
	onLeave: function(origin, destination, direction, trigger) {
		if (destination.index > 0) {
			document.getElementById("topo").classList.add('recolhido');
		} else {
			document.getElementById("topo").classList.remove('recolhido');
		}
	}
});

document.addEventListener( 'DOMContentLoaded', function() {
	new Splide( '.banners', {
		pagination: false
	} ).mount();

	fotos_emp = new Splide( '.fotos-empreendimento', {
		arrows: true,
		pagination: false
	} ).mount();

	const legendas = ["Hall de Entrada","Salão de Festas<br>e Espaço Gourmet","Academia","Academia","Playground"];
	fotos_emp.on( 'moved', function (index) {
		document.getElementById("legenda-fotos").innerHTML = legendas[index];
	} );

	if (isPortrait) {
		new Splide( '.detalhes-splide', {
			pagination: false
		} ).mount();

		new Splide( '.diferenciais-splide', {
			pagination: false
		} ).mount();
	}
} );

function abrirImagem(url) {
	const body = document.getElementsByTagName('body');

	var elemDiv = document.createElement('div');
	elemDiv.innerHTML = '<img src="'+url+'">';
	elemDiv.classList.add("fullscreen-img");
	elemDiv.onclick = function() {
		elemDiv.remove();
	}
	document.body.appendChild(elemDiv);
}

function moverPara(slide) {
	fp.moveTo(slide);
}

function isPortrait() {
	return window.innerHeight > window.innerWidth;
}