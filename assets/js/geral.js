document.addEventListener("DOMContentLoaded", function() {

	$('.form-recaptcha button').on('click', function () {
		formname = '#'+$(this).attr('data-formname');
		console.log('Formulario: '+formname);
		if ( $(formname)[0].checkValidity() == true) {
			console.log('executar recaptcha');
			grecaptcha.execute();
		} else {
			$(formname).find('input[type="submit"]').click()
			return true;
		}

		var retorno = false;

		onSubmit = function(token) {
			console.log('onSubmit');
			$(formname).find('textarea[name="g-recaptcha-response"]').val(token);
			$(formname)[0].submit();
			return true;
		};

		return false;
	});

	const abrir_menu = document.getElementById('btn-menu');
	const menu = document.getElementById('menu');
	const app = document.getElementById('app');
	const fechar_menu = document.getElementById('fechar-menu');

	abrir_menu.addEventListener('click', function (event) {
		menu.classList.add('active');
		app.classList.add('active');
	});

	fechar_menu.addEventListener('click', function (event) {
		menu.classList.remove('active');
		app.classList.remove('active');
	});

	window.onscroll = function() {recolherMenu()};
});

function abrirJanela(url, larg, alt) {
	window.open(url, '', 'width=' + larg + ',height=' + alt + ',left=340,top=300,resizable=no,menubar=no,location=no,status=no,scrollbars=no');
}

function recolherMenu() {
	if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
		document.getElementById("topo").classList.add('recolhido');
	} else {
		document.getElementById("topo").classList.remove('recolhido');
	}
}

function fecharMenu() {
	const menu = document.getElementById('menu');
	const app = document.getElementById('app');
	menu.classList.remove('active');
	app.classList.remove('active');
}