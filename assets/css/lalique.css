/* -----              Sliders              -----*/
.splide__arrow {
	height: 70px;
	width: 70px;
	background-color: rgb(2 2 2 / 80%);
	background-repeat: no-repeat;
	background-position: center center;
	background-size: auto 60%;
	border-radius: 36px;
	opacity: 1;
}

.splide__arrow svg {
	display: none;
}

.splide__arrow--prev {
	background-image: url(../img/lalique/seta-esq-branca.svg);
}

.splide__arrow--next {
	background-image: url(../img/lalique/seta-dir-branca.svg);
}

/* ---------------------------------------------*/


/* -----           Banner Inicial          -----*/

.slide-inicial {
	height: 100%;
}

.slide-inicial .texto {
	position: absolute;
	text-align: center;
	bottom: 40px;
	width: 100%;
	z-index: 1;
	color: #ffffff;
}

.slide-inicial h1 {
	font-size: 56px;
	margin-bottom: 10px;
	font-weight: 400;
}

.slide-inicial .subtitulo {
	font-size: 24px;
	font-weight: 300;
}

.banners {
	height: 100%;
}

.banners .splide__track {
	height: 100%;
}

.foto-banner {
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center center;
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}

.foto-banner1 { background-image: url(../img/lalique/carrossel/01-banner-fachada-final.jpg); }
.foto-banner2 { background-image: url(../img/lalique/carrossel/apartamento-01.jpg); }
.foto-banner3 { background-image: url(../img/lalique/carrossel/apartamento-02.jpg); }
.foto-banner4 { background-image: url(../img/lalique/carrossel/apartamento-03.jpg); }
.foto-banner5 { background-image: url(../img/lalique/carrossel/apartamento-04.jpg); }
.foto-banner6 { background-image: url(../img/lalique/carrossel/apartamento-05.jpg); }
.foto-banner7 { background-image: url(../img/lalique/carrossel/apartamento-06.jpg); }
.foto-banner8 { background-image: url(../img/lalique/carrossel/apartamento-07.jpg); }
.foto-banner9 { background-image: url(../img/lalique/carrossel/apartamento-08.jpg); }
.foto-banner10 { background-image: url(../img/lalique/carrossel/apartamento-09.jpg); }
.foto-banner11 { background-image: url(../img/lalique/carrossel/apartamento-10.jpg); }
.foto-banner12 { background-image: url(../img/lalique/carrossel/apartamento-11.jpg); }
.foto-banner13 { background-image: url(../img/lalique/carrossel/apartamento-12.jpg); }
.foto-banner14 { background-image: url(../img/lalique/carrossel/apartamento-13.jpg); }
.foto-banner15 { background-image: url(../img/lalique/carrossel/empreendimento-01.jpg); }
.foto-banner16 { background-image: url(../img/lalique/carrossel/empreendimento-02.jpg); }
.foto-banner17 { background-image: url(../img/lalique/carrossel/empreendimento-03.jpg); }
.foto-banner18 { background-image: url(../img/lalique/carrossel/empreendimento-04.jpg); }
.foto-banner19 { background-image: url(../img/lalique/carrossel/empreendimento-05.jpg); }
/* ---------------------------------------------*/


/* -----      Sobre o Empreendimento       -----*/
.empreendimento {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-top: 150px;
	padding-bottom: 50px;
	height: 100%;
}

.texto-empreendimento {
	width: 33%;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	font-size: 32px;
	font-weight: 300;
	color: #575757;
}

.texto-empreendimento p {
	display: block;
	margin: 0 auto;
	width: 300px;
	max-width: 90%;
}

.texto-empreendimento .legenda {
	display: block;
	margin: 0 auto;
	width: 300px;
	max-width: 90%;
	bottom: 0;
	font-size: 28px;
}

.fotos-empreendimento {
	width: 67%;
	height: 100%;
}

.fotos-empreendimento .splide__track {
	height: 100%;
}

.foto-emp {
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center center;
	width: 100%;
	height: 100%;
}

.fotos-empreendimento .splide__pagination {
	text-align: left;
	text-align: left;
	align-items: flex-start;
	justify-content: flex-start;
	bottom: -40px;
	padding: 0;
}

.fotos-empreendimento .splide__pagination__page {
	width: 70px;
	height: 12px;
	border-radius: 5px;
	background: #B5B5B5;
}

.fotos-empreendimento .splide__pagination__page.is-active {
	transform: inherit;
	background: #575757;
}

.foto-emp1 { background-image: url(../img/lalique/area_comum/empreendimento-06.jpg); }
.foto-emp2 { background-image: url(../img/lalique/area_comum/empreendimento-07.jpg); }
.foto-emp3 { background-image: url(../img/lalique/area_comum/empreendimento-08.jpg); }
.foto-emp4 { background-image: url(../img/lalique/area_comum/empreendimento-09.jpg); }
.foto-emp5 { background-image: url(../img/lalique/area_comum/empreendimento-10.jpg); }

/* ---------------------------------------------*/


/* -----        Detalhes Área Comum        -----*/
.detalhes-area-comum {
	background: #ffffff;
	min-height: 100%;
	padding-top: 130px;
}

.detalhes-area-comum h2 {
	text-align: center;
	font-weight: 300;
	font-size: 42px;
	color: #575757;
}

.detalhes-area-comum .splide {
	display: none;
}

.detalhes-area-comum .grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	height: calc(100vh - 180px)
}

.detalhes-area-comum .grid-item {
	width: 30% !important;
	height: 33%;
	text-align: center;
	display: flex;
	justify-content: space-between;
	flex-direction: column;
	padding: 20px 0;
}

.detalhes-area-comum .grid-item i {
	display: block;
	height: calc(100% - 50px);
	background-position: center center;
	background-repeat: no-repeat;
}

.detalhes-area-comum .grid-item p {
	height: 40px;
	display: block;
}

.ico-elevador { background-image: url(../img/lalique/icones/ico-elevador.png); background-size: contain;}
.ico-ar-condicionado { background-image: url(../img/lalique/icones/ico-ar-condicionado.png); }
.ico-decoracao { background-image: url(../img/lalique/icones/ico-decoracao.png); background-size: contain; }
.ico-revestimento { background-image: url(../img/lalique/icones/ico-revestimento.png); }
.ico-bicicleta { background-image: url(../img/lalique/icones/ico-bicicleta.png); }
.ico-carro { background-image: url(../img/lalique/icones/ico-carro.png); }
.ico-tomada { background-image: url(../img/lalique/icones/ico-tomada.png); }
.ico-led { background-image: url(../img/lalique/icones/ico-led.png); }
.ico-seguranca { background-image: url(../img/lalique/icones/ico-seguranca.png); background-size: contain; }
/* ---------------------------------------------*/


/* -----              Plantas              -----*/
.plantas {
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;
	align-items: center;
	padding: 20px 3%;
	padding-top: 120px;
	height: 100%;
	background: #ffffff;
}

.plantas h2 {
	text-align: center;
	font-weight: 300;
	font-size: 42px;
	color: #575757;
	width: 100%;
	margin-bottom: 50px;
	height: 50px;
}

.plantas .planta {
	height: calc(100% - 100px);
	overflow: hidden;
	width: 60%;
	text-align: center;
	font-size: 20px;
	font-weight: 300;
}

.plantas .planta img {
	width: 100%;
	height: calc(100% - 50px);
	margin-bottom: 10px;
	object-fit: cover;
	cursor: pointer;
}

.plantas .conteudo {
	width: 40%;
	padding-left: 50px;
	font-size: 36px;
	font-weight: 300;
}

.plantas .conteudo p {
	margin-bottom: 50px;
}

.plantas .conteudo b {
	font-weight: 900;
	color: #58595B;
}

.plantas .conteudo .informacoes b {
	font-size: 72px;
	vertical-align: sub;
}

.plantas .conteudo .saiba-mais {
	font-family: 'Lato';
	width: 170px;
	height: 46px;
	line-height: 42px;
	display: block;
	font-size: 20px;
	font-weight: 300;
	border: 1px solid #575B5C;
	border-radius: 5px;
	text-align: center;
	background: transparent;
	color: #58595B;
}

.plantas2 {
	flex-direction: row-reverse;
}

/* ---------------------------------------------*/


/* -----            Diferenciais           -----*/
.diferenciais {
	background: #ffffff;
	min-height: 100%;
	padding-top: 130px;
}

.diferenciais h2 {
	text-align: center;
	font-weight: 300;
	font-size: 42px;
	color: #575757;
}

.diferenciais .grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	height: calc(100vh - 180px)
}

.diferenciais .grid-item {
	width: 25%;
	height: 50%;
	text-align: center;
	display: flex;
	justify-content: space-between;
	flex-direction: column;
	padding: 20px 20px;
}

.diferenciais .grid-item i {
	display: block;
	height: calc(100% - 100px);
	background-position: center center;
	background-repeat: no-repeat;
}

.diferenciais .grid-item p {
	height: 130px;
	display: block;
}

.ico-ar-condicionado { background-image: url(../img/lalique/icones/ico-ar-condicionado.png); }
.ico-persianas { background-image: url(../img/lalique/icones/ico-persianas.png); }
.ico-sol { background-image: url(../img/lalique/icones/ico-sol.png); }
.ico-pia { background-image: url(../img/lalique/icones/ico-pia.png); }
.ico-regua { background-image: url(../img/lalique/icones/ico-regua.png); }
.ico-chuveiro { background-image: url(../img/lalique/icones/ico-chuveiro.png); }
.ico-usb { background-image: url(../img/lalique/icones/ico-usb.png); }
.ico-fechadura { background-image: url(../img/lalique/icones/ico-fechadura.png); }

.diferenciais .splide {
	display: none;
}
/* ---------------------------------------------*/


/* -----            Tour Virtual           -----*/
.tour-virtual {
	min-height: 100%;
	padding-top: 150px;
	text-align: center;
	position: relative;
	background: url(../img/lalique/img-tour-virtual.jpg) center center no-repeat;
	background-size: cover;
}

.tour-virtual h2 {
	text-align: center;
	font-weight: 300;
	font-size: 52px;
	color: #ffffff;
	margin-bottom: 20px;
}

.tour-virtual .botao-link-virtual {
	width: 250px;
	height: 50px;
	border-radius: 5px;
	background-color: #383838;
	text-align: center;
	line-height: 50px;
	display: block;
	font-size: 18px;
	font-weight: 300;
	margin: 0 auto;
}

.tour-virtual .frase {
	height: 115px;
	display: flex;
	align-items: center;
	justify-content: center;
	position: absolute;
	left: 0;
	bottom: 0;
	width: 100%;
	font-size: 26px;
	font-weight: 300;
	color: #ffffff;
	background: rgb(2 2 2 / 50%);
	line-height: 36px;
}
/* ---------------------------------------------*/


/* -----             Endereço              -----*/
.box-endereco {
	padding: 50px 0;
	padding-top: 150px;
	height: 100%;
	display: flex;
	align-items: center;
	background-color: #ffffff;
}

.box-endereco .conteudo {
	max-width: 1400px;
	height: 100%;
	display: flex;
	justify-content: space-between;
}

.box-endereco .texto {
	width: 40%;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	padding-right: 60px;
}

.box-endereco .texto .titulo {
	color: #58595B;
	font-size: 24px;
	font-weight: 600;
	margin-bottom: 5px;
}

.box-endereco .texto .bairro {
	margin-bottom: 50px;
	color: #58595B;
	font-size: 20px;
	font-weight: 300;
}

.box-endereco .endereco {
	font-size: 22px;
	font-weight: 300;
}

.box-endereco .redes-sociais {
	text-align: left;
}

.box-endereco .redes-sociais .botoes {
	display: flex;
	justify-content: space-between;
	width: 270px;
	text-align: center;
}

.box-endereco .redes-sociais .botao {
	display: block;
	width: 50px;
	height: 50px;
	margin: 0 auto;
	margin-bottom: 5px;
}

.box-endereco .redes-sociais .whatsapp {
	background: url(../img/ico-whats-rodape.svg) center center no-repeat;
	background-size: auto 80%;
}

.box-endereco .redes-sociais .facebook {
	background: url(../img/ico-facebook-rodape.svg) center center no-repeat;
	background-size: auto 80%;
}

.box-endereco .redes-sociais .twitter {
	background: url(../img/ico-twitter-rodape.svg) center center no-repeat;
	background-size: 80%;
}

.box-mapa {
	width: 60%;
	display: flex;
	align-items: center;
	justify-content: center;
	background: url(../img/lalique/img-mapa.jpg) center center;
	background-size: cover;
}

.box-mapa .btn {
	width: 250px;
	height: 55px;
	line-height: 53px;
	display: block;
	font-size: 20px;
	border-radius: 5px;
	background: #383838;
	color: #ffffff;
	text-align: center;
	font-weight: 300;
	font-size: 22px;
}


/* ---------------------------------------------*/


/* -----        Imagem em Fullscreen       -----*/
	.fullscreen-img {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(0,0,0,0.8);
		z-index: 12;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
	}

	.fullscreen-img img {
		object-fit: cover;
		height: 90%
	}

	.fullscreen-img::after {
		content: "X";
		position: absolute;
		top: 10px;
		right: 20px;
		font-size: 32px;
		color: #ffffff;
	}
/* ---------------------------------------------*/


/* -----     Definições de breakpoints     -----*/

@media screen and (max-width: 1200px) {
	
}

@media screen and (max-width: 1100px) {

}

@media screen and (max-height: 810px) {
	.plantas .planta img {
		width: auto;
	}
}

@media screen and (max-height: 720px) {
	.detalhes-area-comum .grid-item i,
	.diferenciais .grid-item i {
		background-size: auto 75%;
	}
}

@media screen and (max-height: 660px) {
	
}

/* Dispositivos na vertical */
@media screen and (orientation:portrait) {
	.empreendimento {
		flex-direction: column;
		background-color: #F5F5F5;
	}

	.texto-empreendimento {
		width: 100%;
		height: 300px;
		text-align: center;
	}

	.fotos-empreendimento {
		height: calc(100% - 320px);
		width: 100%;
	}

	.fotos-empreendimento .splide__pagination {
		align-items: center;
		justify-content: center;
	}

	.detalhes-area-comum h2,
	.diferenciais h2 {
		position: absolute;
		width: 100%;
	}

	.detalhes-area-comum .grid,
	.diferenciais .grid {
		display: none;
	}

	.detalhes-area-comum .splide,
	.diferenciais .splide {
		display: block;
		height: 80%;
		margin-top: 60px;
	}

	.detalhes-area-comum .splide__track,
	.diferenciais .splide__track {
		height: 100%;
	}

	.detalhes-area-comum .grid-item,
	.diferenciais .grid-item {
		height: 100%;
		width: 100% !important;
	}

	.detalhes-area-comum .grid-item i,
	.diferenciais .grid-item i {
		background-size: auto;
	}

	.detalhes-area-comum .splide__arrow,
	.diferenciais .splide__arrow {
		background-color: #828081;
	}

	.plantas {
		flex-direction: column;
		flex-direction: column-reverse;
		flex-wrap: nowrap;
		padding-top: 250px;
		height: auto;
	}

	.plantas h2 {
		position: absolute;
		width: 100%;
		top: 120px;
	}

	.plantas .conteudo {
		height: 400px;
		width: 100%;
		text-align: center;
		padding: 0;
	}

	.plantas .conteudo .saiba-mais {
		width: 90%;
		margin: 0 auto;
	}

	.plantas .planta {
		width: 100%;
		height: 550px;
	}

	.plantas .planta img {
		width: auto;
	}

	.fullscreen-img img {
		width: 90%;
		height: auto;
	}

	.diferenciais {
		background: #F5F5F5;
	}

	.box-endereco {
		flex-direction: column;
	}

	.box-endereco .conteudo {
		max-height: inherit;
		flex-direction: column;
	}

	.box-mapa {
		width: 100%;
		height: 300px;
		margin: 0 auto;
	}

	.box-endereco .texto {
		height: auto;
		min-height: 500px;
		width: 100%;
		padding: 0;
		text-align: center;
	}

	.box-endereco .texto .titulo {
		white-space: nowrap;
	}

	.box-endereco .texto .titulo br {
		content: "";
	}

	.box-endereco .texto .titulo br:after {
		content: " ";
	}

	.box-endereco .redes-sociais {
		text-align: center;
	}

	.box-endereco .redes-sociais .botoes {
		margin: 0 auto;
	}
}

/* Celulares em geral */
@media screen and (max-width: 680px),
screen and (orientation:landscape) and (max-height: 500px) {
	.splide__arrow {
		width: 40px;
		height: 40px;
	}
	
	.slide-inicial h1 {
		font-size: 32px;
	}

	.empreendimento {
		height: auto;
		padding-top: 120px;
	}

	.texto-empreendimento {
		padding-top: 0;
		margin-bottom: 20px;
		height: 250px;
	}

	.fotos-empreendimento {
		height: 400px;
	}

	.fotos-empreendimento .splide__pagination__page {
		width: 50px;
	}

	.plantas .planta {
		height: auto;
	}

	.plantas .planta img {
		width: 100%;
		height: auto;
	}

	.plantas2 {
		padding-top: 120px;
	}

	.tour-virtual .frase {
		font-size: 14px;
		line-height: 20px;
	}

	.box-endereco {
		height: auto;
	}

	.box-endereco .texto {
		min-height: auto;
		margin-bottom: 30px;
	}

	.box-endereco .texto .titulo {
		white-space: inherit;
		font-size: 20px;
	}

	.box-endereco .redes-sociais {
		margin-top: 30px;
	}
}
/* ---------------------------------------------*/