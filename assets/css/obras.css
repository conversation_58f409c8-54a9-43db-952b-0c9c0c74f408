.banner {
	width: 100%;
	height: 370px;
	background: url(../img/banner-pg-faleconosco.jpg) top center no-repeat;
}

.conteudo {
	padding-top: 40px;
}

.conteudo h1 {
	text-align: center;
	color: #595A5C;
	text-transform: uppercase;
	font-size: 32px;
	margin-bottom: 20px;
}

.conteudo .subtitulo {
	color: #595A5C;
	text-align: center;
	margin-bottom: 60px;
	font-weight: 600;
}

.obra {
	display: flex;
	align-content: space-between;
	align-items: center;
	color: #A5A6AA;
	padding: 45px 0;
	border-bottom: 2px solid #CACACC;
}

.obra .ano {
	font-weight: 300;
	font-size: 52px;
}

.obra .nome {
	text-transform: uppercase;
	font-size: 52px;
	font-weight: 400;
	margin: 10px 0;
}

.obra .localizacao {
	text-transform: uppercase;
	font-weight: 300;
	font-size: 24px;
}

.obra .separador {
	margin: 0 15px;
}

.obra:nth-child(odd) .imagem {
	margin-right: 40px;
}

.obra:nth-child(even) {
	flex-direction: row-reverse;
}

.obra:nth-child(even) .imagem {
	margin-left: 40px;
}



/* -----     Definições de breakpoints     -----*/


/* Celulares em geral */
@media screen and (max-width: 680px),
screen and (orientation:landscape) and (max-height: 500px) {
	
	.obra {
		flex-wrap: wrap;
		flex-direction: column;
	}

	.obra .imagem {
		width: 100%;
		text-align: center;
		margin: 0 !important;
	}

	.obra .imagem img {
		max-width: 90%;
	}

	.obra .texto {
		width: 100%;
		text-align: center;
	}

	.obra .nome {
		font-size: 32px;
	}
}
/* ---------------------------------------------*/