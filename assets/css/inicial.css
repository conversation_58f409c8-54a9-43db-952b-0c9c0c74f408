/* -----           Banner Inicial          -----*/

.banners {
	position: relative;
	width: 100%;
	height: 100%;
	color: #ffffff;
	font-weight: 300;
}

.banner {
	position: relative;
	width: 100%;
	height: 100%;
	background: url(../img/banner-home.jpg) center center no-repeat;
	padding-bottom: 40px;
	display: flex;
	flex-direction: column;
	justify-content: flex-end;
}

.banner .conteudo {
	height: 60px;
	text-align: center;
}

.banner .texto {
	font-size: 20px;
	line-height: 45px;
}

.banner .texto .separador {
	margin: 0 15px;
}

/* ---------------------------------------------*/


/* -----          Empreendimentos          -----*/
	.imovel {
		height: 100%;
		padding-bottom: 40px;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;
		color: #ffffff;
		text-align: center;
		background: center center no-repeat;
		font-weight: 300;
	}

	.imovel.lalique {
		background-image: url(../img/bnnr-lalique.png);
	}

	.imovel.piazza {
		background-image: url(../img/bnnr-piazza685.png);
	}

	.imovel.breve {
		background-image: url(../img/bnnr-sta-catarina.png);
	}

	.imovel.breve .selo {
		position: absolute;
		background: url(../img/carimbo-sta-catarina.png) center center no-repeat;
		background-size: contain;
		height: 147px;
		width: 100%;
		top: calc(50% - 73px);
	}

	.imovel .data {
		font-size: 24px;
		margin-bottom: 15px;
	}

	.imovel .separador {
		margin: 0 15px;
	}

	.imovel .nome {
		font-size: 56px;
		margin-bottom: 10px;
		font-weight: 400;
	}

	.imovel .subtitulo {
		font-size: 24px;
		margin-bottom: 30px;
		font-weight: 300;
	}

	.imovel .btn {
		width: 135px;
		height: 46px;
		line-height: 42px;
		display: block;
		margin: 0 auto;
		font-size: 20px;
		border: 1px solid #ffffff;
		border-radius: 5px;
	}

	.imovel.breve .btn {
		width: 195px;
	}
/* ---------------------------------------------*/


/* -----     Definições de breakpoints     -----*/

	@media screen and (max-height: 700px) {
		.imovel.breve .selo {
			top: 30%;
		}
	}

	@media screen and (max-width: 1200px) {
		.banner .texto {
			padding-left: 300px;
			padding-right: 120px;
		}
	}

	@media screen and (max-width: 1100px) {

		.banner .btn-conheca {
			width: 200px;
			font-size: 18px;
		}

		.banner .texto {
			padding-left: 200px;
			padding-right: 120px;
		}
	}

	/* Dispositivos na vertical */
	@media screen and (orientation:portrait) {
		.banner {
			background-size: auto 115%;
		}

		.imovel {
			background-size: auto 125%;
		}
	}

	/* Celulares em geral */
	@media screen and (max-width: 680px),
	screen and (orientation:landscape) and (max-height: 500px) {
		
		.banner {
			padding-bottom: 80px;
		}

		.banner .btn-conheca {
			height: 50px;
			line-height: 50px;
			position: relative;
			margin: 0 auto;
		}

		.banner .texto {
			padding: 0;
		}

		.imovel .data {
			font-size: 20px;
		}

		.imovel .nome {
			font-size: 32px;
		}

		.imovel .subtitulo {
			font-size: 20px;
		}

		.imovel .btn {
			height: 45px;
			line-height: 43px;
			font-size: 20px;
		}

		.imovel.breve .selo {
			position: absolute;
			background: url(../img/carimbo-sta-catarina.png) center center no-repeat;
			background-size: contain;
			height: 120px;
			width: 100%;
			top: calc(50% - 100px);
		}
	}
/* ---------------------------------------------*/