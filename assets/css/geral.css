/* -----          <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gerais        -----*/

* {
	padding: 0;
	margin: 0;
	box-sizing: border-box;
}

html, body {
	min-height: 100%;
	height: 100%;
}

body {
	position: relative;
	font-family: "Lato", sans-serif;
	font-size: 16px;
	font-weight: 400;
	color: #818183;
	background: #DFDFDF;
	min-height: 100%;
	max-width: 100%;
	overflow-x: hidden;
}

::-webkit-input-placeholder {
	color: #232323;
	opacity: 1;
}

::-moz-placeholder {
	color: #232323;
	opacity: 1;
}

:-ms-input-placeholder {
	color: #232323;
	opacity: 1;
}

a {
	outline: none;
	text-decoration: none;
	color: #ffffff;
}

a img {
	border: 0;
}

b {
	font-weight: 700;
}

input,
textarea {
	font-family: "Roboto", sans-serif;
	font-size: 16px;
	font-weight: 400;
	color: #232323;
	background: transparent;
	resize: none;
	outline: none;
	padding: 0 10px;
}

button {
	height: 50px;
	border: 0;
	border-radius: 10px;
	cursor: pointer;
	color: #ffffff;
}

.hidden {
	display: none;
}

.conteudo, .center {
	margin: 0 auto;
	width: 95%;
	max-width: 1720px;
}

.clear {
	clear: both;
}

.tablet,
.mobile {
	display: none;
}

/* ---------------------------------------------*/


/* -----                Topo               -----*/

.topo {
	height: 120px;
	position: absolute;
	top: 10px;
	width: 100%;
	z-index: 1;
}

.topo .center {
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 100%;
}

.logo {
	width: 140px;
	height: 100%;
	background: url(../img/logo-abcr-branca.svg) no-repeat;
}

.btn-menu {
	width: 100px;
	height: 48px;
	border: 1px solid #ffffff;
	border-radius: 5px;
	cursor: pointer;
	color: #ffffff;
	text-align: center;
	line-height: 44px;
	font-size: 24px;
	font-weight: 300;
}

.topo.recolhido {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100px;
	background: #EFEFEF;
}

.topo.recolhido .logo {
	background-image: url(../img/logo-abcr-rodape.svg);
	background-position: center center;
	height: 90px;
}

.topo.recolhido .btn-menu {
	height: 45px;
	border: 1px solid #B7B8BC;
	color: #B7B8BC;
}

/* ---------------------------------------------*/


/* -----                Menu               -----*/

.menu {
	position: fixed;
	width: 440px;
	height: 100%;
	max-width: 95%;
	right: -500px;
	top: 0;
	background: #F5F5F5;
	transition: all 0.5s;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	z-index: 12;
	color: #979798;
	text-align: center;
	padding: 70px;
	padding-bottom: 30px;
	font-weight: 300;
}

.menu.active {
	right: 0;
}

.menu a {
	color: #979798;
}

.menu .fechar {
	position: absolute;
	right: 70px;
	top: 30px;
	width: 55px;
	height: 55px;
	background: url(../img/ico-close-menu.svg) center center no-repeat;
	cursor: pointer;
}

.itens-menu {
	height: calc(100% - 200px);
	max-height: 400px;
	margin-top: 50px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.itens-menu li {
	list-style-type: none;
}

.itens-menu li a {
	font-size: 18px;
	padding-bottom: 15px;
	border-bottom: 1px solid #979798;
	width: 100%;
	display: block
}

.contatos-menu {
	height: 100px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.contatos-menu .telefones {
	display: flex;
	justify-content: space-between;
	font-size: 12px;
	text-align: left;
}

.contatos-menu .telefones b {
	display: block;
}

.contatos-menu .redes-sociais {
	display: flex;
	justify-content: space-between;
}

.contatos-menu .redes-sociais .item {
	width: 40px;
	height: 40px;
	border-radius: 50%;
	border: 1px solid #979798;
}

.contatos-menu .redes-sociais .item a {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.contatos-menu .redes-sociais .item svg {
	height: 60%;
}

.section {
	transition: ease 0.7s;
}

#app.active .section {
	filter: blur(4px);
}

/* ---------------------------------------------*/


/* -----              Contato              -----*/

	.section .contato {
		padding-top: 120px;
	}

	.contato .conteudo {
		max-width: 1200px;
		padding: 70px 0;
		border-bottom: 1px solid #D6D6D6;
	}

	.contato .titulo {
		font-weight: 700;
		font-size: 32px;
		text-align: center;
		margin-bottom: 15px;
	}

	.contato .subtitulo {
		font-size: 24px;
		text-align: center;
		margin-bottom: 40px;
	}

	.contato form {
		font-weight: 300;
		font-size: 18px;
	}

	.contato form label {
		display: block;
		margin-bottom: 10px;
	}

	.contato form input {
		width: 100%;
		border: 0;
		border-radius: 5px;
		background: #ffffff;
		padding: 0 15px;
		height: 55px;
		margin-bottom: 25px;
	}

	.contato form input[type="radio"] {
		width: 16px;
		height: 16px;
		margin: 0;
		margin-right: 50px;
		background: #BABBBD;
		border: 0;
	}

	.contato form textarea {
		width: 100%;
		height: 130px;
		background: #ffffff;
		border: 0;
		border-radius: 5px;
		padding: 15px;
	}

	.grid2 {
		display: flex;
		justify-content: space-between;
	}

	.grid2 .box {
		width: 49%;
	}

	.grid2 .grid2 .box {
		width: 48%;
	}

	.grid3 {
		display: flex;
		justify-content: space-between;
		text-transform: none;
	}

	.contato button {
		background: #393939;
		color: #fff;
		width: 250px;
		height: 60px;
		text-transform: uppercase;
		font-size: 20px;
		line-height: 45px;
		text-align: center;
		border-radius: 0;
	}

	.contato .radios {
		display: -ms-flexbox;
		display: -webkit-flex;
		display: flex;
		-webkit-flex-direction: row;
		-ms-flex-direction: row;
		flex-direction: row;
		-webkit-flex-wrap: wrap;
		-ms-flex-wrap: wrap;
		flex-wrap: wrap;
		-webkit-justify-content: space-between;
		-ms-flex-pack: justify;
		justify-content: space-between;
		-webkit-align-content: flex-start;
		-ms-flex-line-pack: start;
		align-content: flex-start;
		-webkit-align-items: flex-start;
		-ms-flex-align: start;
		align-items: flex-start;
	}

	.contato .radios label {
		background-size: auto 25px;
		background-position: 25px center;
		background-repeat: no-repeat;
		line-height: 25px;
		margin-right: 10px;
	}

	.contato .radios .whatsapp {
		background-image: url(../img/ico-whats-form.svg);
	}

	.contato .radios .email {
		background-image: url(../img/ico-mail-form.svg);
		background-size: auto 18px;
	}

	.contato .radios .telefone {
		background-image: url(../img/ico-phone-form.svg);
	}
/* ---------------------------------------------*/


/* -----              Rodapé              -----*/
	.rodape .conteudo {
		max-width: 1200px;
		padding: 70px 0;
		padding-bottom: 140px;
		display: flex;
		font-size: 14px;
		color: #A5A6AA;
	}

	.rodape a {
		color: #A5A6AA;
	}

	.rodape .logo {
		width: 160px;
		height: 145px;
		background: url(../img/logo-abcr-rodape.svg) center center no-repeat;
		margin-right: 40px;
	}

	.rodape .contatos {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.rodape .contatos .box {
		display: flex;
	}

	.rodape .contatos .item {
		padding-left: 40px;
		background-position: top left;
		background-repeat: no-repeat;
		background-size: auto 30px;
		margin-right: 50px;
	}

	.rodape .contatos .item b {
		display: block;
		white-space: nowrap;
	}

	.rodape .contatos .item span {
		font-weight: 300;
	}

	.rodape .telefone {
		background-image: url(../img/ico-fone-rodape.svg);
	}

	.rodape .whatsapp {
		background-image: url(../img/ico-whats-rodape.svg);
	}

	.rodape .email {
		background-image: url(../img/ico-email-rodape.svg);
	}

	.rodape .endereco {
		background-image: url(../img/ico-local-rodape.svg);
	}

	.rodape .redes-sociais {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		min-width: 240px;
	}

	.rodape .redes-sociais span {
		vertical-align: top;
	}

	.rodape .redes-sociais .icone {
		margin-left: 20px;
	}

	.rodape .redes-sociais .icone svg {
		color: #A5A6AA;
		height: 20px;
	}

	.rodape .desenvolvedor {
		text-align: right;
	}

	.rodape .desenvolvedor svg {
		color: #A5A6AA;
		width: 100px;
		height: 55px;
	}

	.chatfixo {
		width: 64px;
		height: 64px;
		border: 0;
		background: rgba(60,59,58, 0.9);
		position: fixed;
		bottom: 40px;
		right: 40px;
		border-radius: 50%;
		padding: 0;
		z-index: 11;
	}
	
	.chatfixo .whatsapp {
		display: block;
		height: 100%;
		width: 100%;
		border-radius: 50%;
		background: url(../img/ico-whats-share.svg) center center no-repeat;
		background-size: 60%;
		left: 5px;
		top: 5px;
	}
	
	.chatfixo .pulse {
		width: 100%;
		height: 100%;
		transform: scale(1);
		animation: pulse-black 2.5s infinite;
		border-radius: 50%;
		border: 1px solid rgba(60, 59, 58, 1);
		position: absolute;
		pointer-events: none;
	}
	
	@keyframes pulse-black {
		0% {
			transform: scale(1);
		}
		40% {
			transform: scale(2.2);
			border: 1px solid rgba(46,46,46, 0);
		}
		100% {
			transform: scale(2);
			border: 1px solid rgba(46,46,46, 0);
		}
	}
/* ---------------------------------------------*/


/* -----     Definições de breakpoints     -----*/

	@media screen and (max-width: 1200px) {
		
	}

	@media screen and (max-width: 1100px) {
		.rodape .conteudo {
			flex-wrap: wrap;
			justify-content: space-around;
			padding-bottom: 200px;
		}

		.rodape .contatos .item.email {
			margin-right: 0;
		}

		.rodape .redes-sociais {
			margin-top: 110px;
			position: absolute;
			right: 5%;
		}
	}

	/* Dispositivos na vertical */
	@media screen and (orientation:portrait) {
		.grid2 {
			flex-wrap: wrap;
		}

		.grid2 .box {
			width: 100%;
		}

		.contato .radios {
			margin: 15px 0;
		}

		.contato button {
			margin-top: 15px;
		}

		.rodape .conteudo {
			padding-bottom: 50px;
		}

		.rodape .logo {
			margin: 0;
			margin-bottom: 30px;
		}

		.rodape .contatos {
			width: 100%;
		}

		.rodape .contatos .box {
			flex-wrap: wrap;
		}

		.rodape .contatos .item {
			width: 100%;
			margin: 0;
			margin-bottom: 20px;
			text-align: center;
			padding: 0;
			padding-top: 40px;
			background-position: top center;
		}

		.rodape .redes-sociais {
			position: relative;
			right: inherit;
			margin-top: 20px;
			text-align: center;
		}

		.rodape .redes-sociais span {
			width: 100%;
			display: block;
			margin-bottom: 10px;
		}

		.rodape .desenvolvedor {
			text-align: center;
			margin-top: 60px;
		}
	}

	/* Celulares em geral */
	@media screen and (max-width: 680px),
	screen and (orientation:landscape) and (max-height: 500px) {

		.topo {
			height: 70px;
		}

		.logo {
			background: url(../img/logo-abcr-branca.svg) no-repeat;
			width: 140px;
			height: 88px
		}
		.topo.recolhido .logo{
			height: 63px;
		}
		
		.btn-menu {
			height: 40px;
			width: 100px;
			line-height: 36px;
		}

		.topo.recolhido {
			height: 70px;
		}

		.topo.recolhido .btn-menu {
			height: 40px;
		}

		.menu {
			max-width: 90%;
			padding: 30px;
		}

		.menu .fechar {
			right: 30px;
			width: 40px;
			height: 40px;
			top: 20px;
		}

		.contato .titulo {
			font-size: 18px;
		}

		.contato .subtitulo {
			font-size: 18px;
			font-weight: 300;
		}

		.contato form input {
			margin-bottom: 15px;
			height: 45px;
		}

		.grid2 .grid2 .box {
			width: 100%;
		}

		.contato form label {
			margin-bottom: 5px;
			font-size: 14px;
		}

		.contato .radios label {
			background-size: auto 15px;
			font-size: 12px;
			line-height: inherit;
		}

		.contato .radios .email {
			background-size: auto 10px;
		}

		.contato form input[type="radio"] {
			margin-right: 30px;
		}

		.contato button, .contato .g-recaptcha {
			margin: 7px auto;
		}

		.chatfixo {
			width: 50px;
			height: 50px;
			bottom: 20px;
			right: 20px;
		}

		.chatfixo .whatsapp {
			width: 50px;
			height: 50px;
		}

		.chatfixo .pulse {
			width: 50px;
			height: 50px;
		}
	}
/* ---------------------------------------------*/