RewriteEngine on
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php/$1 [L]

RewriteEngine on
RewriteCond %{HTTP_HOST} ^piazza685.com.br
RewriteRule ^ http://www.piazza685.com.br%{REQUEST_URI} [L,R=301]

<IfModule mod_headers.c>
  <FilesMatch "\.(ico|gif|jpg|jpeg|png|svg)$">
        Header set Cache-Control "max-age=31536000"
  </FilesMatch>
  <FilesMatch "\.(css|js)$">
        Header set Cache-Control "max-age=31536000"
  </FilesMatch>
</IfModule>

FileETag None
<IfModule mod_expires.c>
 ExpiresActive on
 ExpiresDefault "access plus 1 month"
 ExpiresByType text/css "access plus 1 year"
 ExpiresByType application/javascript "access plus 1 year"
 ExpiresByType application/x-javascript  "access plus 1 year"
 ExpiresByType text/cache-manifest "access plus 0 seconds"
 ExpiresByType text/html "access plus 0 seconds"
 ExpiresByType text/xml "access plus 0 seconds"
 ExpiresByType application/xml "access plus 0 seconds"
 ExpiresByType application/json "access plus 0 seconds"
 ExpiresByType text/x-component "access plus 1 month"
 ExpiresByType application/x-font-ttf "access plus 1 month"
 ExpiresByType font/opentype "access plus 1 month"
 ExpiresByType application/x-font-woff "access plus 1 month"
 ExpiresByType image/svg+xml "access plus 1 month"
 ExpiresByType application/vnd.ms-fontobject "access plus 1 month"
 ExpiresByType image/x-icon "access plus 1 week"
 ExpiresByType image/gif "access plus 1 month"
 ExpiresByType image/png "access plus 1 month"
 ExpiresByType image/jpg "access plus 1 month"
 ExpiresByType image/jpeg "access plus 1 month"
 ExpiresByType video/ogg "access plus 1 month"
 ExpiresByType audio/ogg "access plus 1 month"
 ExpiresByType video/mp4 "access plus 1 month"
 ExpiresByType video/webm "access plus 1 month"
</IfModule>

<ifModule mod_gzip.c>
  mod_gzip_on Yes
  mod_gzip_dechunk Yes
  mod_gzip_item_include file .(html?|txt|css|js|php|pl|svg)$
  mod_gzip_item_include handler ^cgi-script$
  mod_gzip_item_include mime ^text/.*
  mod_gzip_item_include mime ^application/x-javascript.*
  mod_gzip_item_include mime ^image/image/svg+xml
  mod_gzip_item_exclude mime ^image/.*
  mod_gzip_item_exclude rspheader ^Content-Encoding:.*gzip.*
</ifModule>

<IfModule mod_deflate.c>
  # Compress HTML, CSS, JavaScript, Text, XML and fonts
  AddOutputFilterByType DEFLATE image/svg+xml
  AddOutputFilterByType DEFLATE image/x-icon
  AddOutputFilterByType DEFLATE text/css
  AddOutputFilterByType DEFLATE text/html
  AddOutputFilterByType DEFLATE text/plain
  AddOutputFilterByType DEFLATE text/xml
  AddOutputFilterByType DEFLATE text/javascript
  AddOutputFilterByType DEFLATE application/javascript
  AddOutputFilterByType DEFLATE application/json
</IfModule>